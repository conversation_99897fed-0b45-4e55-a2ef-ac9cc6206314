from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import requests
import os
import json
from datetime import datetime, timedelta
import subprocess
from collections import defaultdict
import time
import logging
from logging.handlers import RotatingFileHandler
from config import AppConfig
import hashlib
import pickle

# Load environment variables from .env file if it exists
try:
    from dotenv import load_dotenv
    load_dotenv()  # This loads variables from .env file
    print("✅ Environment variables loaded from .env file")
except ImportError:
    print("⚠️  python-dotenv not installed. Using system environment variables only.")
    print("   Install with: pip install python-dotenv")

app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Simple in-memory cache for Airtable data
class SimpleCache:
    """
    Simple in-memory cache with TTL (Time To Live) support.
    Perfect for caching Airtable responses to reduce API calls.
    """

    def __init__(self):
        self._cache = {}
        self._timestamps = {}

    def _generate_key(self, base_id, table_id, filter_formula, max_records):
        """Generate a unique cache key for the request parameters"""
        key_data = f"{base_id}:{table_id}:{filter_formula or ''}:{max_records}"
        return hashlib.md5(key_data.encode()).hexdigest()

    def get(self, base_id, table_id, filter_formula=None, max_records=1000, ttl_seconds=300):
        """
        Get cached data if it exists and is not expired.

        Args:
            base_id: Airtable base ID
            table_id: Airtable table ID
            filter_formula: Optional filter formula
            max_records: Maximum records requested
            ttl_seconds: Time to live in seconds (default: 5 minutes)

        Returns:
            Cached data or None if not found/expired
        """
        cache_key = self._generate_key(base_id, table_id, filter_formula, max_records)

        if cache_key not in self._cache:
            return None

        # Check if cache entry has expired
        cache_time = self._timestamps.get(cache_key, 0)
        if time.time() - cache_time > ttl_seconds:
            # Remove expired entry
            del self._cache[cache_key]
            del self._timestamps[cache_key]
            return None

        logger.debug(f"🎯 Cache HIT for key: {cache_key[:8]}...")
        return self._cache[cache_key]

    def set(self, base_id, table_id, data, filter_formula=None, max_records=1000):
        """
        Store data in cache with current timestamp.

        Args:
            base_id: Airtable base ID
            table_id: Airtable table ID
            data: Data to cache
            filter_formula: Optional filter formula
            max_records: Maximum records requested
        """
        cache_key = self._generate_key(base_id, table_id, filter_formula, max_records)
        self._cache[cache_key] = data
        self._timestamps[cache_key] = time.time()

        logger.debug(f"💾 Cache SET for key: {cache_key[:8]}... (size: {len(data.get('records', []))} records)")

    def clear(self):
        """Clear all cached data"""
        self._cache.clear()
        self._timestamps.clear()
        logger.info("🗑️ Cache cleared")

    def get_stats(self):
        """Get cache statistics"""
        total_entries = len(self._cache)
        total_size = sum(len(str(data)) for data in self._cache.values())

        return {
            'total_entries': total_entries,
            'total_size_bytes': total_size,
            'cache_keys': list(self._cache.keys())
        }

# Initialize cache
cache = SimpleCache()

# Configure structured logging
def setup_logging():
    """Configure structured logging with different levels and file rotation"""

    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # Configure logging format
    log_format = logging.Formatter(
        AppConfig.LOG_FORMAT,
        datefmt=AppConfig.LOG_DATE_FORMAT
    )

    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, AppConfig.LOG_LEVEL))

    # Console handler (for development)
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(log_format)

    # File handler with rotation (for production)
    file_handler = RotatingFileHandler(
        'logs/server.log',
        maxBytes=AppConfig.LOG_FILE_MAX_BYTES,
        backupCount=AppConfig.LOG_BACKUP_COUNT
    )
    file_handler.setLevel(getattr(logging, AppConfig.LOG_LEVEL))
    file_handler.setFormatter(log_format)

    # Error file handler (separate file for errors)
    error_handler = RotatingFileHandler(
        'logs/errors.log',
        maxBytes=AppConfig.LOG_FILE_MAX_BYTES,
        backupCount=AppConfig.LOG_BACKUP_COUNT
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(log_format)

    # Add handlers to logger
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    logger.addHandler(error_handler)

    # Configure Flask's logger
    app.logger.setLevel(logging.INFO)

    return logger

# Initialize logging
logger = setup_logging()
logger.info("[STARTUP] Analytics Dashboard Server starting up...")
logger.info(f"[INFO] Working directory: {os.getcwd()}")
logger.info(f"[INFO] Python version: {os.sys.version}")

# API Configuration - Using environment variables for security
CLAUDE_API_KEY = os.getenv('CLAUDE_API_KEY')
CLAUDE_API_URL = AppConfig.CLAUDE_API_URL

AIRTABLE_API_KEY = os.getenv('AIRTABLE_API_KEY')
AIRTABLE_BASE_URL = AppConfig.AIRTABLE_BASE_URL

# Validate that required environment variables are set
if not CLAUDE_API_KEY:
    raise ValueError("CLAUDE_API_KEY environment variable is required")
if not AIRTABLE_API_KEY:
    raise ValueError("AIRTABLE_API_KEY environment variable is required")

@app.route('/')
def index():
    return send_from_directory('.', 'index.html')

@app.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint that verifies all critical components are working.
    Returns detailed status information for monitoring and debugging.
    """
    try:
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'version': '1.0.0',
            'components': {}
        }

        # Check environment variables
        try:
            if CLAUDE_API_KEY and AIRTABLE_API_KEY:
                health_status['components']['environment'] = {
                    'status': 'healthy',
                    'message': 'All required environment variables are set'
                }
            else:
                health_status['components']['environment'] = {
                    'status': 'unhealthy',
                    'message': 'Missing required environment variables'
                }
                health_status['status'] = 'unhealthy'
        except Exception as e:
            health_status['components']['environment'] = {
                'status': 'error',
                'message': f'Environment check failed: {str(e)}'
            }
            health_status['status'] = 'unhealthy'

        # Check file system (logs directory)
        try:
            if os.path.exists('logs') and os.access('logs', os.W_OK):
                health_status['components']['filesystem'] = {
                    'status': 'healthy',
                    'message': 'Logs directory is accessible'
                }
            else:
                health_status['components']['filesystem'] = {
                    'status': 'warning',
                    'message': 'Logs directory not accessible (will use console only)'
                }
        except Exception as e:
            health_status['components']['filesystem'] = {
                'status': 'error',
                'message': f'Filesystem check failed: {str(e)}'
            }

        # Test Airtable connectivity (optional quick test)
        try:
            # Just test if we can make a basic request (don't actually fetch data)
            test_url = f"{AIRTABLE_BASE_URL}/meta"  # Airtable meta endpoint
            test_headers = {'Authorization': f'Bearer {AIRTABLE_API_KEY}'}

            # Quick connectivity test with short timeout
            test_response = requests.get(test_url, headers=test_headers, timeout=5)

            if test_response.status_code in [200, 401, 403]:  # Any response means connectivity works
                health_status['components']['airtable'] = {
                    'status': 'healthy',
                    'message': 'Airtable API is reachable'
                }
            else:
                health_status['components']['airtable'] = {
                    'status': 'warning',
                    'message': f'Airtable API returned status {test_response.status_code}'
                }
        except requests.exceptions.Timeout:
            health_status['components']['airtable'] = {
                'status': 'warning',
                'message': 'Airtable API timeout (may be slow)'
            }
        except Exception as e:
            health_status['components']['airtable'] = {
                'status': 'warning',
                'message': f'Airtable connectivity test failed: {str(e)}'
            }

        # Determine overall status
        component_statuses = [comp['status'] for comp in health_status['components'].values()]
        if 'error' in component_statuses or health_status['status'] == 'unhealthy':
            health_status['status'] = 'unhealthy'
            status_code = 503
        elif 'warning' in component_statuses:
            health_status['status'] = 'degraded'
            status_code = 200
        else:
            health_status['status'] = 'healthy'
            status_code = 200

        logger.info(f"🏥 Health check completed: {health_status['status']}")
        return jsonify(health_status), status_code

    except Exception as e:
        logger.error(f"💥 Health check failed: {str(e)}", exc_info=True)
        return jsonify({
            'status': 'error',
            'timestamp': datetime.now().isoformat(),
            'message': f'Health check failed: {str(e)}'
        }), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    try:
        # Get the request data from the frontend
        data = request.json

        logger.info(f"💬 Chat request received with {len(data.get('messages', []))} messages")
        logger.debug(f"📝 Full request: {json.dumps(data, indent=2)}")

        # Extract system message and user messages
        messages = data.get('messages', [])
        system_message = None
        filtered_messages = []

        for message in messages:
            if message.get('role') == 'system':
                system_message = message.get('content')
            else:
                filtered_messages.append(message)

        # Prepare the request to Claude API with correct format
        claude_request = {
            'model': data.get('model', 'claude-3-opus-20240229'),
            'max_tokens': data.get('max_tokens', 4000),
            'temperature': data.get('temperature', 0.7),
            'messages': filtered_messages
        }

        # Add system message if present
        if system_message:
            claude_request['system'] = system_message

        logger.info(f"🤖 Sending request to Claude API (model: {claude_request['model']})")
        logger.debug(f"📤 Claude request: {json.dumps(claude_request, indent=2)}")

        # Prepare headers
        headers = {
            'Content-Type': 'application/json',
            'x-api-key': CLAUDE_API_KEY,
            'anthropic-version': '2023-01-01'
        }

        # Forward the request to Claude API with timeout
        try:
            response = requests.post(
                CLAUDE_API_URL,
                json=claude_request,
                headers=headers,
                timeout=AppConfig.CLAUDE_API_TIMEOUT
            )
        except requests.exceptions.Timeout:
            logger.error("⏱️ Claude API request timed out after 30 seconds")
            return jsonify({"error": "Request timed out. Please try again."}), 504
        except requests.exceptions.ConnectionError:
            logger.error("🔌 Failed to connect to Claude API")
            return jsonify({"error": "Unable to connect to AI service. Please try again."}), 503
        except requests.exceptions.RequestException as e:
            logger.error(f"🚫 Claude API request failed: {str(e)}")
            return jsonify({"error": "AI service request failed. Please try again."}), 502

        # Check if the request was successful
        if response.status_code != 200:
            logger.error(f"❌ Claude API error: {response.status_code} - {response.text}")
            return jsonify({"error": f"Claude API returned status code {response.status_code}"}), response.status_code

        # Return the response from Claude API
        logger.info("✅ Claude API request successful")
        return jsonify(response.json())

    except Exception as e:
        logger.error(f"💥 Unexpected error in /api/chat: {str(e)}", exc_info=True)
        return jsonify({"error": str(e)}), 500

def validate_airtable_params(base_id, table_id, max_records_str, filter_formula):
    """
    Validate and sanitize input parameters for Airtable API requests.
    Returns (validated_params, error_message) tuple.
    """
    errors = []

    # Validate base_id format (should start with 'app' and be 17 chars)
    if not base_id:
        errors.append("baseId is required")
    elif not base_id.startswith('app') or len(base_id) != 17:
        errors.append("baseId must start with 'app' and be 17 characters long")

    # Validate table_id format (should start with 'tbl' and be 17 chars)
    if not table_id:
        errors.append("tableId is required")
    elif not table_id.startswith('tbl') or len(table_id) != 17:
        errors.append("tableId must start with 'tbl' and be 17 characters long")

    # Validate max_records (must be positive integer, reasonable limit)
    max_records = AppConfig.MAX_TOTAL_RECORDS  # Default from config
    if max_records_str:
        try:
            max_records = int(max_records_str)
            if max_records <= 0:
                errors.append("maxRecords must be a positive integer")
            elif max_records > AppConfig.MAX_TOTAL_RECORDS:
                errors.append(f"maxRecords cannot exceed {AppConfig.MAX_TOTAL_RECORDS:,} (configured limit)")
        except ValueError:
            errors.append("maxRecords must be a valid integer")

    # Validate filter formula (basic sanitization)
    if filter_formula:
        # Check for potentially dangerous patterns
        dangerous_patterns = ['javascript:', 'eval(', 'script>', 'DROP TABLE', 'DELETE FROM']
        filter_lower = filter_formula.lower()
        for pattern in dangerous_patterns:
            if pattern in filter_lower:
                errors.append(f"Filter formula contains potentially dangerous pattern: {pattern}")

        # Check length (Airtable has limits)
        if len(filter_formula) > 1000:
            errors.append("Filter formula is too long (max 1000 characters)")

    if errors:
        return None, "; ".join(errors)

    return {
        'base_id': base_id,
        'table_id': table_id,
        'max_records': max_records,
        'filter_formula': filter_formula
    }, None

@app.route('/api/airtable/records', methods=['GET'])
def get_airtable_records():
    try:
        # Get and validate query parameters
        base_id = request.args.get('baseId')
        table_id = request.args.get('tableId')
        max_records_str = request.args.get('maxRecords', '1000')
        filter_formula = request.args.get('filterByFormula')

        # Validate input parameters
        validated_params, error_msg = validate_airtable_params(
            base_id, table_id, max_records_str, filter_formula
        )

        if error_msg:
            logger.warning(f"🚫 Validation error: {error_msg}")
            return jsonify({"error": f"Validation error: {error_msg}"}), 400

        # Extract validated parameters
        base_id = validated_params['base_id']
        table_id = validated_params['table_id']
        max_records = validated_params['max_records']
        filter_formula = validated_params['filter_formula']

        logger.info(f"📊 Airtable request: baseId={base_id}, tableId={table_id}, maxRecords={max_records}")
        if filter_formula:
            logger.debug(f"🔍 Filter formula: {filter_formula}")

        # Check cache first (5 minute TTL for most requests, 1 minute for filtered requests)
        cache_ttl = 60 if filter_formula else 300  # Shorter TTL for filtered data
        cached_data = cache.get(base_id, table_id, filter_formula, max_records, cache_ttl)

        if cached_data:
            logger.info(f"🎯 Returning cached data: {len(cached_data.get('records', []))} records")
            return jsonify(cached_data)

        # Prepare Airtable API request
        url = f"{AIRTABLE_BASE_URL}/{base_id}/{table_id}"
        headers = {
            'Authorization': f'Bearer {AIRTABLE_API_KEY}',
            'Content-Type': 'application/json'
        }

        params = {
            'maxRecords': max_records
        }

        # Add optional filters
        if request.args.get('filterByFormula'):
            params['filterByFormula'] = request.args.get('filterByFormula')

        if request.args.get('sort'):
            params['sort'] = request.args.get('sort')

        # Add pagination support
        if request.args.get('offset'):
            params['offset'] = request.args.get('offset')

        logger.info(f"🌐 Fetching Airtable data from: {url}")
        logger.debug(f"📋 Initial params: {params}")

        # Implement server-side pagination to fetch ALL records
        all_records = []
        offset = None
        page_count = 0
        max_pages = AppConfig.MAX_PAGINATION_PAGES  # Safety limit from config
        cursor_filter = None  # For cursor-based pagination

        logger.info(f"🚨🚨🚨 UPDATED CODE VERSION 2.0 - Starting server-side pagination to fetch up to {max_records} records...")
        logger.info(f"DEBUG: Initial offset from client: {request.args.get('offset')}")

        while page_count < max_pages:
            page_count += 1

            # Set up params for this page
            page_params = {}

            # 🔧 CRITICAL FIX: Only set maxRecords if we need to limit the total
            # Setting maxRecords=100 tells Airtable to return max 100 records TOTAL, not per page!
            # This was preventing pagination from working properly
            remaining_records = max_records - len(all_records)
            logger.info(f"🔧 DEBUG: remaining_records={remaining_records}, MAX_RECORDS_PER_REQUEST={AppConfig.MAX_RECORDS_PER_REQUEST}")
            logger.info(f"🔧 DEBUG: Condition check: {remaining_records} < {AppConfig.MAX_RECORDS_PER_REQUEST} = {remaining_records < AppConfig.MAX_RECORDS_PER_REQUEST}")

            if remaining_records < AppConfig.MAX_RECORDS_PER_REQUEST:
                # Only limit when we're close to our target
                page_params['maxRecords'] = remaining_records
                logger.info(f"🎯 Setting maxRecords={remaining_records} to limit total results")
            else:
                # Let Airtable use its default page size (100) and provide offset for pagination
                logger.info(f"🔄 No maxRecords limit - allowing Airtable native pagination")
            if offset:
                page_params['offset'] = offset

            # Only add specific parameters that we know work
            if 'filterByFormula' in params and params['filterByFormula']:
                page_params['filterByFormula'] = params['filterByFormula']
                logger.info(f"🔧 DEBUGGING: Using original filter: {params['filterByFormula']}")
            elif cursor_filter:
                # Use cursor filter for pagination
                page_params['filterByFormula'] = cursor_filter
                logger.info(f"🔧 DEBUGGING: Using cursor filter: {cursor_filter}")
            else:
                logger.info(f"🔧 DEBUGGING: No filter applied")

            # Add sorting based on table configuration
            table_config = AppConfig.get_table_config(table_id)
            if table_config:
                page_params['sort[0][field]'] = table_config['date_field']
                page_params['sort[0][direction]'] = table_config['sort_direction']
                logger.debug(f"🔧 Using {table_config['name']} configuration for sorting")

            logger.debug(f"📄 Fetching page {page_count} with offset: {offset}")
            logger.debug(f"🔧 Page params: {page_params}")

            # Make request to Airtable with timeout and error handling
            try:
                logger.info(f"DEBUG: Request URL: {url}")
                logger.info(f"DEBUG: Request params: {page_params}")
                response = requests.get(
                    url,
                    headers=headers,
                    params=page_params,
                    timeout=AppConfig.AIRTABLE_API_TIMEOUT
                )
            except requests.exceptions.Timeout:
                logger.error(f"⏱️ Airtable API request timed out on page {page_count}")
                return jsonify({"error": "Request timed out while fetching data. Please try again."}), 504
            except requests.exceptions.ConnectionError:
                logger.error(f"🔌 Failed to connect to Airtable API on page {page_count}")
                return jsonify({"error": "Unable to connect to data service. Please try again."}), 503
            except requests.exceptions.RequestException as e:
                logger.error(f"🚫 Airtable API request failed on page {page_count}: {str(e)}")
                return jsonify({"error": "Data service request failed. Please try again."}), 502

            # Check response status
            if response.status_code == 429:
                logger.warning(f"🚦 Rate limited by Airtable API on page {page_count}")
                return jsonify({"error": "Too many requests. Please wait a moment and try again."}), 429
            elif response.status_code == 401:
                logger.error(f"🔐 Unauthorized Airtable API request on page {page_count}")
                return jsonify({"error": "Invalid API credentials. Please check configuration."}), 401
            elif response.status_code == 404:
                logger.error(f"🔍 Airtable resource not found on page {page_count}")
                return jsonify({"error": "Requested data not found. Please check table ID."}), 404
            elif response.status_code != 200:
                logger.error(f"❌ Airtable API error on page {page_count}: {response.status_code} - {response.text}")
                return jsonify({"error": f"Data service error (status {response.status_code}). Please try again."}), 502

            data = response.json()
            page_records = data.get('records', [])

            # Transform and add records from this page
            for record in page_records:
                flattened_record = {
                    'id': record['id'],
                    'createdTime': record['createdTime'],
                    **record['fields']
                }
                all_records.append(flattened_record)

            logger.info(f"Page {page_count}: {len(page_records)} records (Total so far: {len(all_records)})")

            # Check if we have more pages
            offset = data.get('offset')
            logger.info(f"DEBUG: Airtable response has offset: {bool(offset)}")
            logger.info(f"DEBUG: Response keys: {list(data.keys())}")
            logger.info(f"DEBUG: Records in this page: {len(page_records)}")
            if offset:
                logger.info(f"DEBUG: Offset value: {offset[:50]}...")
            else:
                logger.info(f"DEBUG: No offset in response - checking if this is expected")
                logger.info(f"DEBUG: Page records length: {len(page_records)}, expected 100 for full page")

            if offset:
                # 🎉 GREAT! Airtable provided an offset - use it for next page
                logger.info(f"✅ Airtable provided offset - continuing with native pagination")
                # The loop will continue automatically with the offset
                continue
            else:
                logger.debug(f"No offset provided by Airtable API.")
                # 🔧 CRITICAL FIX: Check if we should continue pagination
                # Continue if we got a full page (100 records) AND haven't reached our limit
                # This works for both Airtable offset pagination AND our custom cursor pagination
                if len(page_records) == 100 and len(all_records) < max_records:
                    logger.info(f"🔄 Got exactly 100 records but no offset. Attempting cursor-based pagination...")
                    # Get the last record's date to use as cursor
                    last_record = page_records[-1]
                    last_date = None
                    date_field = None

                    # Determine the date field based on table configuration
                    table_config = AppConfig.get_table_config(table_id)
                    if table_config:
                        date_field = table_config['date_field']
                        last_date = last_record.get('fields', {}).get(date_field)
                        logger.debug(f"📅 Using {table_config['name']} date field: {date_field}")
                    else:
                        date_field = None
                        last_date = None
                        logger.warning(f"⚠️ Unknown table ID: {table_id}")

                    if last_date and date_field:
                        logger.info(f"📅 Using cursor date: {last_date} from field: {date_field}")
                        # 🔧 DEBUGGING: Check if we're missing records with the same date
                        logger.info(f"🔧 DEBUGGING: About to filter records before {last_date}")
                        logger.info(f"🔧 DEBUGGING: Current total records: {len(all_records)}")

                        # Create a filter to get records older than the last date
                        # but exclude records we've already fetched by using record ID
                        last_record_id = last_record.get('id')
                        logger.info(f"🔧 DEBUGGING: Last record ID: {last_record_id}")

                        # 🔧 CRITICAL FIX: Use much smaller pages to minimize same-date record loss
                        # The core issue: IS_BEFORE excludes ALL records with same date
                        # Solution: Use tiny pages (25 records) to reduce same-date conflicts
                        logger.info(f"🚨 NEW CODE EXECUTING: Using micro-pagination to avoid same-date conflicts")

                        # Use very small page size to minimize records lost due to same-date exclusion
                        page_params['maxRecords'] = 25  # Tiny pages to reduce same-date conflicts

                        # 🚨 RADICAL CHANGE: Abandon ALL filtering - use pure offset-based pagination
                        # The IS_BEFORE filter is the root cause of missing records
                        # New approach: Use NO FILTER and manually track what we've seen
                        cursor_filter = None  # NO FILTER AT ALL
                        logger.info(f"🚨 RADICAL CHANGE: Using NO FILTER to avoid any record exclusions")
                        logger.info(f"🔧 DEBUGGING: Will rely on sorting and manual stopping only")

                        # Infinite loop detection: check if we're getting the same records
                        current_page_ids = [r.get('id') for r in page_records]
                        if 'previous_page_ids' not in locals():
                            previous_page_ids = []

                        # If we're getting the same records as last time, stop
                        if current_page_ids == previous_page_ids:
                            logger.warning(f"🔄 Same records detected - stopping to prevent infinite loop")
                            break

                        previous_page_ids = current_page_ids

                        # Continue with NO filter - just keep fetching with sorting
                        offset = None  # Reset offset, use NO filter
                        continue
                    else:
                        logger.warning(f"⚠️ No date field found for cursor pagination.")
                        break
                else:
                    logger.info(f"✅ Pagination complete.")
                    break

            # Check if we've reached our target
            if len(all_records) >= max_records:
                logger.info(f"🎯 Reached target of {max_records} records. Stopping pagination.")
                all_records = all_records[:max_records]  # Trim to exact limit
                break

            # If we got less than 100 records, we've reached the end
            if len(page_records) < 100:
                logger.info(f"🔚 Got {len(page_records)} records (less than 100). End of data reached.")
                break

        # 🔧 FINAL DEDUPLICATION: Remove any duplicate records by ID
        seen_ids = set()
        unique_records = []
        original_count = len(all_records)

        for record in all_records:
            record_id = record.get('id')
            if record_id not in seen_ids:
                seen_ids.add(record_id)
                unique_records.append(record)
            else:
                logger.debug(f"🔧 DEDUPLICATION: Removed duplicate record {record_id}")

        if len(unique_records) != original_count:
            logger.info(f"🔧 DEDUPLICATION: Removed {original_count - len(unique_records)} duplicate records")
            all_records = unique_records

        # Prepare final response
        response_data = {
            'records': all_records,
            'offset': None,  # No offset since we fetched all available records
            'pagination_info': {
                'total_records': len(all_records),
                'pages_fetched': page_count,
                'server_side_pagination': True,
                'duplicates_removed': original_count - len(all_records)
            }
        }

        logger.info(f"Server-side pagination complete: {len(all_records)} total records from {page_count} pages")

        # Cache the successful response
        cache.set(base_id, table_id, response_data, filter_formula, max_records)

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"💥 Unexpected error in /api/airtable/records: {str(e)}", exc_info=True)
        return jsonify({"error": str(e)}), 500

@app.route('/api/cache', methods=['GET', 'DELETE'])
def manage_cache():
    """
    Cache management endpoint for monitoring and clearing cache.
    GET: Returns cache statistics
    DELETE: Clears all cached data
    """
    try:
        if request.method == 'GET':
            # Return cache statistics
            stats = cache.get_stats()
            stats['cache_enabled'] = True
            stats['timestamp'] = datetime.now().isoformat()

            logger.info(f"📊 Cache stats requested: {stats['total_entries']} entries")
            return jsonify(stats)

        elif request.method == 'DELETE':
            # Clear cache
            cache.clear()
            logger.info("🗑️ Cache cleared via API request")
            return jsonify({
                'status': 'success',
                'message': 'Cache cleared successfully',
                'timestamp': datetime.now().isoformat()
            })

    except Exception as e:
        logger.error(f"💥 Error in cache management: {str(e)}", exc_info=True)
        return jsonify({"error": str(e)}), 500

# Debug test page for Airtable pagination
@app.route('/debug-airtable')
def debug_airtable():
    """Serve the Airtable debug test page"""
    return send_from_directory('.', 'debug_airtable_test.html')

# Serve static files
@app.route('/<path:path>')
def serve_static(path):
    return send_from_directory('.', path)

if __name__ == '__main__':
    # Check if we're in development or production
    is_development = os.getenv('FLASK_ENV') == 'development' or os.getenv('DEBUG') == 'true'

    if is_development:
        print("🔧 Starting in DEVELOPMENT mode...")
        print("⚠️  Do NOT use this in production!")
        print("🌐 Server will be available at http://localhost:8000")
        app.run(debug=True, port=8000, host='127.0.0.1')
    else:
        print("[PRODUCTION] Starting in PRODUCTION mode...")
        print("[TIP] For best results, use the startup script: python start_server.py")
        print("[SECURITY] This will automatically choose the right production server")
        print("🌐 Server will be available at http://localhost:8000")

        # Fallback to development server with production settings
        print("⚠️  Using Flask development server with production settings...")
        app.run(debug=False, port=8000, host='127.0.0.1', threaded=True)
